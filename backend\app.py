from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON><PERSON><PERSON>, create_access_token, jwt_required, get_jwt_identity
from datetime import timedelta
import os
from dotenv import load_dotenv

# Import models and routes
from models import db, User, Product, ChatSession, ChatMessage, ProductCategory
from services.chatbot_service import ChatbotService
from services.product_service import ProductService
from services.auth_service import AuthService

# Load environment variables
load_dotenv()

def create_app():
    """Application factory pattern"""
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///ecommerce_chatbot.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-change-in-production')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)
    
    # Initialize extensions
    db.init_app(app)
    CORS(app, origins=['http://localhost:5173', 'http://127.0.0.1:5173'])
    jwt = JWTManager(app)
    
    # Initialize services
    chatbot_service = ChatbotService()
    product_service = ProductService()
    auth_service = AuthService()
    
    # Create tables
    with app.app_context():
        db.create_all()
    
    # Authentication Routes
    @app.route('/api/auth/register', methods=['POST'])
    def register():
        """User registration endpoint"""
        try:
            data = request.get_json()
            
            # Validate required fields
            required_fields = ['email', 'password', 'first_name', 'last_name']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'error': f'{field} is required'}), 400
            
            # Check if user already exists
            if User.query.filter_by(email=data['email']).first():
                return jsonify({'error': 'Email already registered'}), 400
            
            # Create new user
            user = auth_service.create_user(
                email=data['email'],
                password=data['password'],
                first_name=data['first_name'],
                last_name=data['last_name']
            )
            
            # Generate access token
            access_token = create_access_token(identity=user.id)
            
            return jsonify({
                'message': 'User registered successfully',
                'access_token': access_token,
                'user': user.to_dict()
            }), 201
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/auth/login', methods=['POST'])
    def login():
        """User login endpoint"""
        try:
            data = request.get_json()
            
            if not data.get('email') or not data.get('password'):
                return jsonify({'error': 'Email and password are required'}), 400
            
            user = auth_service.authenticate_user(data['email'], data['password'])
            
            if not user:
                return jsonify({'error': 'Invalid email or password'}), 401
            
            access_token = create_access_token(identity=user.id)
            
            return jsonify({
                'message': 'Login successful',
                'access_token': access_token,
                'user': user.to_dict()
            }), 200
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/auth/profile', methods=['GET'])
    @jwt_required()
    def get_profile():
        """Get user profile"""
        try:
            user_id = get_jwt_identity()
            user = User.query.get(user_id)
            
            if not user:
                return jsonify({'error': 'User not found'}), 404
            
            return jsonify({'user': user.to_dict()}), 200
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    # Product Routes
    @app.route('/api/products', methods=['GET'])
    def get_products():
        """Get products with optional filtering"""
        try:
            # Get query parameters
            category = request.args.get('category')
            min_price = request.args.get('min_price', type=float)
            max_price = request.args.get('max_price', type=float)
            min_rating = request.args.get('min_rating', type=float)
            search = request.args.get('search')
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            
            products = product_service.search_products(
                category=category,
                min_price=min_price,
                max_price=max_price,
                min_rating=min_rating,
                search=search,
                page=page,
                per_page=per_page
            )
            
            return jsonify({
                'products': [product.to_dict() for product in products.items],
                'total': products.total,
                'pages': products.pages,
                'current_page': products.page,
                'per_page': products.per_page
            }), 200
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/products/<int:product_id>', methods=['GET'])
    def get_product(product_id):
        """Get specific product by ID"""
        try:
            product = Product.query.get(product_id)
            
            if not product:
                return jsonify({'error': 'Product not found'}), 404
            
            return jsonify({'product': product.to_dict()}), 200
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/products/categories', methods=['GET'])
    def get_categories():
        """Get all product categories"""
        try:
            categories = ProductCategory.query.all()
            return jsonify({
                'categories': [category.to_dict() for category in categories]
            }), 200

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/products/price-range', methods=['GET'])
    def get_price_range():
        """Get min and max price range for products"""
        try:
            from sqlalchemy import func
            result = db.session.query(
                func.min(Product.price).label('min_price'),
                func.max(Product.price).label('max_price')
            ).first()

            return jsonify({
                'min_price': float(result.min_price) if result.min_price else 0,
                'max_price': float(result.max_price) if result.max_price else 1000
            }), 200

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    # Chatbot Routes
    @app.route('/api/chatbot/message', methods=['POST'])
    @jwt_required()
    def send_message():
        """Send message to chatbot"""
        try:
            user_id = get_jwt_identity()
            data = request.get_json()
            
            if not data.get('message'):
                return jsonify({'error': 'Message is required'}), 400
            
            session_id = data.get('session_id')
            user_message = data['message']
            
            # Process message through chatbot service
            response = chatbot_service.process_message(
                user_id=user_id,
                message=user_message,
                session_id=session_id
            )
            
            return jsonify(response), 200
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/chatbot/history', methods=['GET'])
    @jwt_required()
    def get_chat_history():
        """Get chat history for user"""
        try:
            user_id = get_jwt_identity()
            session_id = request.args.get('session_id')
            
            history = chatbot_service.get_chat_history(user_id, session_id)
            
            return jsonify({'history': history}), 200
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/chatbot/reset', methods=['POST'])
    @jwt_required()
    def reset_chat():
        """Reset chat session"""
        try:
            user_id = get_jwt_identity()
            data = request.get_json()
            session_id = data.get('session_id')
            
            new_session = chatbot_service.reset_session(user_id, session_id)
            
            return jsonify({
                'message': 'Chat session reset successfully',
                'session_id': new_session.session_id
            }), 200
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    # Health check endpoint
    @app.route('/api/health', methods=['GET'])
    def health_check():
        """Health check endpoint"""
        return jsonify({
            'status': 'healthy',
            'message': 'E-commerce Chatbot API is running'
        }), 200
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
