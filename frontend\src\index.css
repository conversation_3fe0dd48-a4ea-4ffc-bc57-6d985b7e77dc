@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-lg text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700;
  }
  
  .btn-ghost {
    @apply hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100;
  }
  
  .btn-sm {
    @apply h-8 px-3 text-xs;
  }
  
  .btn-md {
    @apply h-10 px-4 py-2;
  }
  
  .btn-lg {
    @apply h-12 px-6 text-base;
  }
  
  .card {
    @apply rounded-xl border bg-white shadow-soft dark:bg-gray-800 dark:border-gray-700;
  }
  
  .input {
    @apply flex h-10 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder:text-gray-400;
  }
  
  .label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }
  
  .text-muted {
    @apply text-gray-600 dark:text-gray-400;
  }
  
  .text-muted-foreground {
    @apply text-gray-500 dark:text-gray-400;
  }
  
  .border-border {
    @apply border-gray-200 dark:border-gray-700;
  }

  .bg-background {
    @apply bg-white dark:bg-gray-900;
  }

  .text-foreground {
    @apply text-gray-900 dark:text-gray-100;
  }
  
  .bg-muted {
    @apply bg-gray-50 dark:bg-gray-800;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .chat-bubble-user {
    @apply bg-primary-600 text-white rounded-2xl rounded-br-md;
  }
  
  .chat-bubble-bot {
    @apply bg-gray-100 text-gray-900 rounded-2xl rounded-bl-md dark:bg-gray-700 dark:text-gray-100;
  }
  
  .typing-indicator {
    @apply flex space-x-1;
  }
  
  .typing-dot {
    @apply w-2 h-2 bg-gray-400 rounded-full animate-bounce;
  }
  
  .typing-dot:nth-child(2) {
    animation-delay: 0.1s;
  }
  
  .typing-dot:nth-child(3) {
    animation-delay: 0.2s;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
