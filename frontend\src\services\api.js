import axios from 'axios'

const API_BASE_URL = 'http://localhost:5000/api'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (email, password) => 
    api.post('/auth/login', { email, password }),
  
  register: (userData) => 
    api.post('/auth/register', userData),
  
  getProfile: () => 
    api.get('/auth/profile'),
}

// Products API
export const productsAPI = {
  getProducts: (params = {}) =>
    api.get('/products', { params }),

  getProduct: (id) =>
    api.get(`/products/${id}`),

  getCategories: () =>
    api.get('/products/categories'),

  getPriceRange: () =>
    api.get('/products/price-range'),

  searchProducts: (params) =>
    api.get('/products', { params }),
}

// Chatbot API
export const chatbotAPI = {
  sendMessage: (message, sessionId = null) => 
    api.post('/chatbot/message', { message, session_id: sessionId }),
  
  getHistory: (sessionId = null) => 
    api.get('/chatbot/history', { params: { session_id: sessionId } }),
  
  resetChat: (sessionId = null) => 
    api.post('/chatbot/reset', { session_id: sessionId }),
}

// Health check
export const healthAPI = {
  check: () => api.get('/health'),
}

export default api
