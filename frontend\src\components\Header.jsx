import React from 'react'
import { <PERSON>, useNavigate } from 'react-router-dom'
import { ShoppingBag, User, LogOut, Moon, Sun, MessageCircle } from 'lucide-react'
import { useAuth } from '../context/AuthContext'
import { useTheme } from '../context/ThemeContext'

const Header = () => {
  const { user, logout } = useAuth()
  const { isDark, toggleTheme } = useTheme()
  const navigate = useNavigate()

  const handleLogout = () => {
    logout()
    navigate('/login')
  }

  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-border sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
            <ShoppingBag className="w-8 h-8 text-primary-600" />
            <h1 className="text-2xl font-bold text-foreground">ShopBot</h1>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link 
              to="/" 
              className="text-foreground hover:text-primary-600 transition-colors font-medium"
            >
              Products
            </Link>
            <Link 
              to="/dashboard" 
              className="text-foreground hover:text-primary-600 transition-colors font-medium flex items-center"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              AI Assistant
            </Link>
          </nav>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            <button
              onClick={toggleTheme}
              className="btn btn-ghost btn-sm"
              title={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
            >
              {isDark ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
            </button>

            {user ? (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <User className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-foreground hidden sm:block">
                    {user.first_name} {user.last_name}
                  </span>
                </div>

                <button
                  onClick={handleLogout}
                  className="btn btn-ghost btn-sm text-red-600 hover:text-red-700"
                  title="Logout"
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            ) : (
              <Link to="/login" className="btn btn-primary btn-sm">
                Sign In
              </Link>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
