import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Star, ShoppingCart, Eye, Heart, Package } from 'lucide-react'
import toast from 'react-hot-toast'

const ProductCard = ({ product, compact = false, viewMode = 'grid' }) => {
  const [isLiked, setIsLiked] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)

  const {
    id,
    name,
    description,
    price,
    category,
    rating,
    image_url,
    availability,
    brand,
    stock_quantity
  } = product

  const handleAddToCart = (e) => {
    e.preventDefault()
    e.stopPropagation()
    toast.success(`${name} added to cart!`)
  }

  const handleLike = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setIsLiked(!isLiked)
    toast.success(isLiked ? 'Removed from wishlist' : 'Added to wishlist')
  }

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  const renderStars = (rating) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
      )
    }

    if (hasHalfStar) {
      stars.push(
        <Star key="half" className="w-4 h-4 fill-yellow-400/50 text-yellow-400" />
      )
    }

    const emptyStars = 5 - Math.ceil(rating)
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Star key={`empty-${i}`} className="w-4 h-4 text-gray-300" />
      )
    }

    return stars
  }

  // List view mode
  if (viewMode === 'list') {
    return (
      <motion.div
        whileHover={{ scale: 1.01 }}
        className="bg-card rounded-lg shadow-sm border border-border hover:shadow-md transition-all duration-200"
      >
        <Link to={`/products/${id}`} className="block">
          <div className="flex p-4 gap-4">
            {/* Image */}
            <div className="relative w-32 h-32 flex-shrink-0">
              {!imageLoaded && (
                <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                  <Package className="w-8 h-8 text-gray-400" />
                </div>
              )}
              <img
                src={image_url || '/api/placeholder/300/300'}
                alt={name}
                className={`w-full h-full object-cover rounded-lg transition-opacity duration-200 ${
                  imageLoaded ? 'opacity-100' : 'opacity-0'
                }`}
                onLoad={() => setImageLoaded(true)}
                onError={(e) => {
                  e.target.style.display = 'none'
                  setImageLoaded(true)
                }}
              />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-lg font-semibold text-foreground truncate pr-2">
                  {name}
                </h3>
                <button
                  onClick={handleLike}
                  className={`p-2 rounded-full transition-colors ${
                    isLiked ? 'text-red-500' : 'text-gray-400 hover:text-red-500'
                  }`}
                >
                  <Heart className={`w-5 h-5 ${isLiked ? 'fill-current' : ''}`} />
                </button>
              </div>

              <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                {description}
              </p>

              <div className="flex items-center gap-2 mb-3">
                <div className="flex items-center">
                  {renderStars(rating || 0)}
                </div>
                <span className="text-sm text-muted-foreground">
                  ({rating || 0})
                </span>
                {category && (
                  <span className="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded-full">
                    {category}
                  </span>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-foreground">
                    {formatPrice(price)}
                  </span>
                </div>

                <button
                  onClick={handleAddToCart}
                  disabled={!availability}
                  className={`btn btn-sm ${
                    availability ? 'btn-primary' : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  {availability ? 'Add to Cart' : 'Unavailable'}
                </button>
              </div>
            </div>
          </div>
        </Link>
      </motion.div>
    )
  }

  if (compact) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ scale: 1.02 }}
        className="card p-3 cursor-pointer hover:shadow-medium transition-shadow"
      >
        <div className="flex space-x-3">
          <img
            src={image_url}
            alt={name}
            className="w-16 h-16 object-cover rounded-lg"
            onError={(e) => {
              e.target.src = `https://via.placeholder.com/64x64/e5e7eb/6b7280?text=${name.charAt(0)}`
            }}
          />
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm text-foreground truncate">{name}</h4>
            <p className="text-xs text-muted-foreground truncate">{brand}</p>
            <div className="flex items-center space-x-1 mt-1">
              {renderStars(rating)}
              <span className="text-xs text-muted-foreground">({rating})</span>
            </div>
            <div className="flex items-center justify-between mt-2">
              <span className="font-bold text-primary-600">${price}</span>
              <span className={`text-xs px-2 py-1 rounded-full ${
                availability 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              }`}>
                {availability ? 'In Stock' : 'Out of Stock'}
              </span>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5 }}
      className="card overflow-hidden group cursor-pointer"
    >
      <Link to={`/products/${id}`} className="block">
      {/* Image */}
      <div className="relative overflow-hidden">
        <img
          src={image_url}
          alt={name}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
          onError={(e) => {
            e.target.src = `https://via.placeholder.com/400x300/e5e7eb/6b7280?text=${name.charAt(0)}`
          }}
        />
        
        {/* Overlay Actions */}
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-3">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 bg-white rounded-full text-gray-800 hover:bg-gray-100"
          >
            <Eye className="w-4 h-4" />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={handleLike}
            className={`p-2 rounded-full transition-colors ${
              isLiked
                ? 'bg-red-500 text-white'
                : 'bg-white text-gray-800 hover:bg-gray-100'
            }`}
          >
            <Heart className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={handleAddToCart}
            className="p-2 bg-primary-600 rounded-full text-white hover:bg-primary-700"
            disabled={!availability}
          >
            <ShoppingCart className="w-4 h-4" />
          </motion.button>
        </div>

        {/* Badges */}
        <div className="absolute top-3 left-3 space-y-2">
          {!availability && (
            <span className="px-2 py-1 bg-red-500 text-white text-xs rounded-full">
              Out of Stock
            </span>
          )}
          {rating >= 4.5 && (
            <span className="px-2 py-1 bg-yellow-500 text-white text-xs rounded-full">
              Top Rated
            </span>
          )}
        </div>

        <div className="absolute top-3 right-3">
          <span className="px-2 py-1 bg-gray-900/70 text-white text-xs rounded-full">
            {category}
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <div className="mb-2">
          <h3 className="font-semibold text-foreground text-lg truncate">{name}</h3>
          <p className="text-sm text-muted-foreground">{brand}</p>
        </div>

        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
          {description}
        </p>

        {/* Rating */}
        <div className="flex items-center space-x-2 mb-3">
          <div className="flex items-center space-x-1">
            {renderStars(rating)}
          </div>
          <span className="text-sm text-muted-foreground">({rating})</span>
          {stock_quantity > 0 && (
            <span className="text-xs text-muted-foreground">
              • {stock_quantity} left
            </span>
          )}
        </div>

        {/* Price and Actions */}
        <div className="flex items-center justify-between">
          <div>
            <span className="text-2xl font-bold text-primary-600">{formatPrice(price)}</span>
          </div>

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleAddToCart}
            disabled={!availability}
            className={`btn btn-sm ${
              availability
                ? 'btn-primary'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            {availability ? 'Add to Cart' : 'Unavailable'}
          </motion.button>
        </div>
        </div>
      </Link>
    </motion.div>
  )
}

export default ProductCard
